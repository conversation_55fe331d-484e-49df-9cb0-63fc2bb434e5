<template>
  <div class="order-tab-content" ref="contentRef">
    <!-- 骨架屏 -->
    <div v-if="showSkeleton" class="skeleton-container">
      <div v-for="i in 3" :key="`skeleton-${i}`" class="order-item">
        <WoCard>
          <div class="skeleton-order">
            <!-- 订单头部骨架 -->
            <div class="skeleton-header">
              <div class="skeleton-order-number"></div>
              <div class="skeleton-status"></div>
            </div>

            <!-- 商品信息骨架 -->
            <div class="skeleton-goods">
              <div class="skeleton-image"></div>
              <div class="skeleton-content">
                <div class="skeleton-title"></div>
                <div class="skeleton-subtitle"></div>
                <div class="skeleton-price"></div>
              </div>
            </div>

            <!-- 按钮骨架 -->
            <div class="skeleton-buttons">
              <div class="skeleton-button"></div>
              <div class="skeleton-button"></div>
            </div>
          </div>
        </WoCard>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!showSkeleton && !loading && orderList.length === 0 && finished" class="empty-state">
      <div class="empty-content">
<!--        <img src="../assets/no-data.png" alt="暂无订单" class="empty-image" />-->
      </div>
    </div>

    <!-- 订单列表 -->
    <van-list v-else v-model:loading="loading" :finished="finished" loading-text="加载中..."  finished-text="没有更多了" @load="onLoad"
              :immediate-check="false">
      <div v-for="order in orderList" :key="order.id" class="order-item">
        <WoCard>
          <!-- 订单头部信息 -->
          <div class="order-header">
            <div class="order-number-container">
              <span class="order-number-text">服务单号：{{ order.afterSaleId || '暂无服务单号'  }}</span>
              <img v-if="order.afterSaleId" src="@/static/images/copy.png" alt="复制" class="copy-icon" @click.stop="copyOrderNumber(order.id)" />
            </div>
            <div class="order-status" >{{ orderState(order.orderState)
              }}</div>
          </div>

          <!-- 商品列表 -->
          <div class="goods-section">
            <!-- 多商品订单：显示为一个整体 -->
            <AfterSaleGoodsCard :key="order.id" :item="order" :image-size="75" :min-height="110" :showActions="true" :itemId="order.id">
              <template #actions>
                <WoButton v-for="action in getItemActions(order)" :key="action.key"
                          :type="action.type || 'primary'" size="small" @click.stop="action.handler">
                  {{ action.label }}
                </WoButton>
              </template>
            </AfterSaleGoodsCard>
          </div>
        </WoCard>
      </div>
    </van-list>

    <ExpirationPopup
      v-model:visible="expirationPopupVisible"
      title=""
      main-text="抱歉，订单已过售后申请时效"
      sub-text="商品已超过售后期限，如需售后可联系客服处理"
      confirm-text="确定"
      @close="expirationPopupVisible = false"
      @confirm="expirationPopupVisible = false"
    />
  </div>
</template>

<script setup>
import {ref, computed, onMounted, onUnmounted, nextTick, markRaw} from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import AfterSaleGoodsCard from '@components/GoodsCommon/AfterSaleGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import useClipboard from 'vue-clipboard3'
import { closeToast, showLoadingToast, showToast } from 'vant'
import orderState from '@/utils/orderState.js'
import {applyOrderAfterSalesJD, getAfterSalesInfo} from '@/api/interface/order.js'
import { getBizCode } from '@/utils/curEnv.js'
import { useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { useAlert } from '@/composables/index.js'
import {afterSalesProduct} from '@/utils/storage.js'
import {get} from "lodash-es";
import ExpirationPopup from "@components/Common/ExpirationPopup/ExpirationPopup.vue";
import {useAfterSalesStore} from "@store/modules/afterSales.js";
import {applyOrderCancel, jdPrompt} from "@/api/index.js";
const { toClipboard } = useClipboard()
const $alert = useAlert()
const props = defineProps({
  tabType: {
    type: String,
    required: true
  },
  scrollPosition: {
    type: Number,
    default: 0
  }
})

const afterSalesStore = useAfterSalesStore()

// 在script setup中添加路由实例
const router = useRouter()

const emit = defineEmits(['update-scroll'])
const contentRef = ref(null)
const loading = ref(false)
const finished = ref(false)
const orderList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalPage = ref(0)
const error = ref(false)
const finishedText = ref('没有更多了')
const isRefreshing = ref(false) // 标识是否正在刷新

const isExpires = (subOrder) => {
  const orderDate = get(subOrder, 'subOrderRawData.orderDate') || get(subOrder, 'expireTime')
  if (!orderDate) return false

  const expDate = dayjs(orderDate).add(15, 'day')
  const now = dayjs()
  return expDate > now
}

const ORDER_STATE = markRaw({
  PENDING_PAYMENT: '0',
  PENDING_DELIVERY_1: '1',
  CANCELLED: '2',
  PENDING_DELIVERY_3: '3',
  PARTIAL_SHIPPED: '4',
  SHIPPING: '5',
  PARTIAL_CANCELLED: '6',
  REJECTED: '7',
  REVOKED: '8',
  COMPLETED: '9',
  REFUNDED: '10',
  PARTIAL_REFUNDED: '11',
  PARTIAL_REFUNDING: '12',
  DELETED: '-1'
})

const expirationPopupVisible = ref(false)

// 获取商品项操作按钮
const getItemActions = (item) => {
  console.warn(231231,item)
  const actions = []
  const orderState = item.orderState
  const isApplyAfterSales = get(item, 'afterSaleId', '')
  const isExpired = !isExpires(item)
  const supplierCode = get(item, 'skuNumInfoList[0].sku.supplierCode', '')
  const isJDSupplier = supplierCode.indexOf('jd_') > -1
  // 售后按钮逻辑
  if (item.applyType) {
    // 有售后类型配置时的逻辑
    if (item.applyType === '1') {
      // 申请退款
      if (isJDSupplier && (orderState === ORDER_STATE.PENDING_DELIVERY_1 || orderState === ORDER_STATE.PENDING_DELIVERY_3 || orderState === ORDER_STATE.SHIPPING) && isApplyAfterSales) {
        actions.push({
          key: 'applyRefund',
          label: '申请退款',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: (item) => handleAfterSalesAction(item, 0)
        })
      } else if (!isJDSupplier && (orderState === ORDER_STATE.PENDING_DELIVERY_1 || orderState === ORDER_STATE.PENDING_DELIVERY_3) && isApplyAfterSales) {
        actions.push({
          key: 'applyRefund',
          label: '申请退款',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: (item) => handleAfterSalesAction(item, 0)
        })
      }
    } else if (item.applyType === '2') {
      // 申请售后
      if (isJDSupplier && orderState === ORDER_STATE.COMPLETED && isApplyAfterSales) {
        actions.push({
          key: 'applyAfterSale',
          label: '申请售后',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: (item) => handleAfterSalesAction(item, 1)
        })
      } else if (!isJDSupplier && (orderState === ORDER_STATE.SHIPPING || orderState === ORDER_STATE.COMPLETED) && isApplyAfterSales) {
        actions.push({
          key: 'applyAfterSale',
          label: '申请售后',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: (item) => handleAfterSalesAction(item, 1)
        })
      }
    }
  } else {
    // 没有售后类型配置时的默认逻辑
    if (isJDSupplier && (orderState === ORDER_STATE.PENDING_DELIVERY_1 || orderState === ORDER_STATE.PENDING_DELIVERY_3 || orderState === ORDER_STATE.SHIPPING) && !isApplyAfterSales) {
      actions.push({
        key: 'applyRefund',
        label: '申请退款',
        type: isExpired && isApplyAfterSales ? 'default' : 'gradient',
        disabled: isExpired,
        handler: () => handleAfterSalesAction(item, 0)
      })
    } else if (!isJDSupplier && (orderState === ORDER_STATE.PENDING_DELIVERY_1 || orderState === ORDER_STATE.PENDING_DELIVERY_3) && !isApplyAfterSales) {
      actions.push({
        key: 'applyRefund',
        label: '申请退款',
        type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
        disabled: isExpired,
        handler: () => handleAfterSalesAction(item, 0)
      })
    }

    if (isJDSupplier && orderState === ORDER_STATE.COMPLETED && !isApplyAfterSales) {
      actions.push({
        key: 'applyAfterSale',
        label: '申请售后',
        type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
        disabled: isExpired,
        handler: () => handleAfterSalesAction(item, 1)
      })
    } else if (!isJDSupplier && (orderState === ORDER_STATE.SHIPPING || orderState === ORDER_STATE.COMPLETED) && !isApplyAfterSales) {
      actions.push({
        key: 'applyAfterSale',
        label: '申请售后',
        type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
        disabled: isExpired,
        handler: () => handleAfterSalesAction(item, 1)
      })
    }
  }
  // 查看详情按钮
  if (isApplyAfterSales) {
    actions.push({
      key: 'viewDetails',
      label: '查看详情',
      type: isExpired && isApplyAfterSales ? 'default' : 'gradient',
      disabled: isExpired,
      handler: () => handleAfterSalesAction(item, 2)
    })
  }
  return actions
}

// 申请售后/退款 - 单个商品
const handleAfterSalesAction = async (item, type) => {
  console.warn(213123,item)
  const isExpired = !isExpires(item)
  const hasAfterSaleId = get(item, 'afterSaleId', '')
  // 如果已过期且没有售后ID，显示过期提示
  if (isExpired && !hasAfterSaleId) {
    expirationPopupVisible.value = true
    return
  }

  const { orderPrice, orderState, supplierSubOrderId, supplierSubOutOrderId } = item
  const { skuNum, sku } = get(item, 'skuNumInfoList[0]', {})
  const { supplierCode } = sku || {}

  // 设置售后产品信息
  const afterSalesProductInfo = {
    supplierSubOrderId,
    orderState,
    orderPrice,
    skuNum,
    sku,
    supplierCode,
    supplierOutSubOrderId: supplierSubOutOrderId || ''
  }
  afterSalesProduct.set(afterSalesProductInfo)

  // 重置售后信息
  if (item.applyType) {
    afterSalesStore.updateAfterSalesInfo({
      applyType: '',
      afterSaleState: '',
      bizOrderId: '',
      bizCode: '',
      orderState: ''
    })
  }

  const isJDSupplier = supplierCode && supplierCode.indexOf('jd_') > -1
  const isHaveGift = +get(item, 'isHaveGift', '0')
  const afterSaleId = item.afterSaleId
  const applyType = item.applyType

  if (isJDSupplier) {
    await handleJDAfterSales(supplierSubOrderId, supplierCode, type, isHaveGift, afterSaleId, applyType, orderState)
  } else {
    await handleNonJDAfterSales(supplierSubOrderId, type, afterSaleId, applyType,orderState)
  }
}

// 处理京东售后
const handleJDAfterSales = async (supplierSubOrderId, supplierCode, type, isHaveGift, afterSaleId, applyType, orderState) => {
  try {
    const [err, res] = await jdPrompt({ supplierSubOrderId, supplierCode })
    if (err) {
      showToast(err.msg)
      return
    }

    const prompt = res

    if (orderState === ORDER_STATE.COMPLETED) {
      // 已签收状态
      if (type === 1) { // 申请售后
        if (isHaveGift) {
          $alert({
            title: '',
            message: '该商品有赠品，如申请售后，请将赠品一同寄回。',
            confirmButtonText: '确定申请',
            cancelButtonText: '暂不申请',
            showCancelButton: true,
            onConfirmCallback: () => {
              applyJDAfterSales(supplierSubOrderId, 1)
            }
          })
          return
        }
        await applyJDAfterSales(supplierSubOrderId, 1)
      } else if (type === 2) { // 查看详情
        await applyJDAfterSales(supplierSubOrderId, 2)
      }
    } else {
      // 其他状态
      if (type === 0) { // 申请退款
        $alert({
          title: '',
          message: prompt,
          confirmButtonText: '确定申请',
          cancelButtonText: '暂不申请',
          showCancelButton: true,
          onConfirmCallback: () => {
            applyJDRefund(supplierSubOrderId)
          }
        })
      } else if (type === 1) { // 申请售后
        await applyJDAfterSales(supplierSubOrderId, 1)
      } else if (type === 2) { // 查看详情
        if (applyType === '1') {
          router.push({
            path: '/wo-after-sales-detail',
            query: {
              afterSaleId,
              type: +applyType
            }
          })
        } else {
          await applyJDAfterSales(supplierSubOrderId, 2)
        }
      }
    }
  } catch (error) {
    showToast('操作失败，请重试')
    console.error('京东售后处理错误:', error)
  }
}

// 处理非京东售后
const handleNonJDAfterSales = async (supplierSubOrderId, type, afterSaleId, applyType,orderState) => {
  try {
    if (type === 0) { // 申请退款
      $alert({
        title: '',
        message: '您确定申请退款吗？',
        confirmButtonText: '确定申请',
        cancelButtonText: '暂不申请',
        showCancelButton: true,
        onConfirmCallback: () => {
          applyRefund(supplierSubOrderId)
        }
      })
    } else if (type === 1) { // 申请售后
      router.push({
        path: '/wo-after-sales-entry',
        query: {
          orderState: orderState
        }
      })
    } else if (type === 2) { // 查看详情
      router.push({
        path: '/wo-after-sales-detail',
        query: {
          afterSaleId,
          type: +applyType
        }
      })
    }
  } catch (error) {
    showToast('操作失败，请重试')
    console.error('非京东售后处理错误:', error)
  }
}

// 京东退款申请
const applyJDRefund = async (supplierSubOrderId) => {
  try {
    showLoadingToast()
    const [err, res] = await applyOrderCancel({ supplierSubOrderId })
    closeToast()

    if (!err) {
      const afterSaleId = res
      router.push({
        path: '/wo-after-sales-detail',
        query: {
          afterSaleId,
          type: 1
        }
      })
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    showToast('申请退款失败')
    console.error('京东退款申请错误:', error)
  }
}

// 京东售后申请
const applyJDAfterSales = async (supplierSubOrderId, firstUrl) => {
  try {
    showLoadingToast()
    const [err, json] = await applyOrderAfterSalesJD({
      supplierSubOrderId,
      firstUrl
    })
    closeToast()

    if (!err) {
      window.location.href = json
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    showToast('申请售后失败')
    console.error('京东售后申请错误:', error)
  }
}

// 非京东退款申请
const applyRefund = async (supplierSubOrderId) => {
  try {
    showLoadingToast()
    const [err, res] = await applyOrderCancel({ supplierSubOrderId })
    closeToast()

    if (!err) {
      const afterSaleId = res
      router.push({
        path: '/wo-after-sales-detail',
        query: {
          afterSaleId,
          type: 1
        }
      })
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    showToast('申请退款失败')
    console.error('退款申请错误:', error)
  }
}
// 骨架屏显示控制
const showSkeleton = computed(() => {
  // 标签页切换时显示骨架屏，或者首次加载且列表为空时显示骨架屏
  return (loading.value && orderList.value.length === 0 && !error.value)
})

// 监听滚动事件，更新滚动位置
const handleScroll = () => {
  if (contentRef.value) {
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    emit('update-scroll', scrollTop)
  }
}

// 复制订单号
const copyOrderNumber = async (orderNumber) => {
  try {
    await toClipboard(orderNumber);
    showToast('复制成功');
  } catch (e) {
    console.error(e);
    showToast('复制失败');
  }
}

// 加载数据
const onLoad = async () => {
  const params = {
    bizCode: getBizCode('ORDER'),
    pageNum: currentPage.value,
    pageSize: pageSize.value
  }

  showLoadingToast()
  const [err, json] = await getAfterSalesInfo(params)
  closeToast()

  if (!err) {
    currentPage.value++
    loading.value = false

    if (json?.afterSaleList.length > 0) {
      finishedText.value = '没有更多了'
    }

    // 如果没有数据，标记为完成
    if (json && json?.afterSaleList.length <= 0) {
      finished.value = true
      return
    }

    const expandedOrders = []
    json.afterSaleList.forEach(order => {
      order.forEach(item => {
        const mergedOrder = {
          ...item,
          skuNumInfoList: item.skuNumInfoList,
          price: item.orderPrice ,
          totalPrice: item.orderPrice
        }

        expandedOrders.push(mergedOrder)
      })
    })
    // 如果是刷新操作，替换数据；否则追加数据
    if (isRefreshing.value) {
      orderList.value = expandedOrders
      isRefreshing.value = false // 重置刷新标识
    } else if (currentPage.value === 2) {
      // 第一页数据，直接替换
      orderList.value = expandedOrders
    } else {
      // 后续页面数据，追加到现有数据
      orderList.value = [...orderList.value, ...expandedOrders]
    }

    // 当前列表有多少页
    totalPage.value = json.totalPage
    if (currentPage.value > totalPage.value) {
      finished.value = true
    }

    // 恢复滚动位置
    if (currentPage.value === 2 && props.scrollPosition > 0) {
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = props.scrollPosition
        }
      })
    }
  } else {
    error.value = true
    loading.value = false
    finished.value = true
  }
}
// 组件挂载时
onMounted(() => {
  // 添加滚动事件监听
  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScroll)
  } else {
    window.addEventListener('scroll', handleScroll)
  }

  // 设置初始加载状态并加载数据
  loading.value = true
  onLoad()
})

// 刷新数据方法（供父组件调用）
const refreshData = async () => {
  try {
    // 设置刷新标识
    isRefreshing.value = true
    // 重置数据状态，但保持刷新标识
    orderList.value = []
    currentPage.value = 1
    finished.value = false
    error.value = false
    loading.value = true
    // 重新加载数据
    await onLoad()
  } catch (error) {
    console.error('刷新数据失败:', error)
    isRefreshing.value = false
    throw error
  }
}

// 暴露方法给父组件
defineExpose({
  refreshData
})

// 组件卸载时
onUnmounted(() => {
  // 移除滚动事件监听
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScroll)
  } else {
    window.removeEventListener('scroll', handleScroll)
  }
})

</script>

<style scoped lang="less">
.order-tab-content {
  min-height: calc(100vh - 134px); // 减去搜索头部、tab头部高度
  padding: 10px;
  background: @bg-color-gray;
}

.order-item {
  margin-bottom: 10px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 11px;

  .order-shop {
    font-size: @font-size-14;
    font-weight: bold;
    color: @text-color-primary;
  }

  .order-number-container {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;

    .order-number-text {
      font-size: @font-size-11;
      color: @text-color-secondary;
      margin-right: 3px;
      .ellipsis()
    }

    .copy-icon {
      width: 10px;
      height: 10px;
      cursor: pointer;
    }
  }


  .countdown-container {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    color: @theme-color;

    .countdown-text {
      font-size: @font-size-14;
      font-weight: @font-weight-600;
      margin-right: 4px;
      color: @theme-color;
    }

    .countdown-time {
      font-size: @font-size-14;
      font-weight: @font-weight-600;
      color: @theme-color;
    }
  }


  .order-status {
    flex-shrink: 0;
    font-size: @font-size-14;
    font-weight: @font-weight-600;

    &.status-unpaid {
      color: @theme-color;
    }

    &.status-unshipped {
      color: #2196f3;
    }

    &.status-shipped {
      color: #4caf50;
    }

    &.status-completed {
      color: @text-color-secondary;
    }
  }
}

.goods-section {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

// 骨架屏样式
.skeleton-container {
  .skeleton-order {
    padding: 0;
  }

  .skeleton-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;

    .skeleton-order-number {
      width: 120px;
      height: 12px;
      background: #f0f0f0;
      border-radius: 6px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }

    .skeleton-status {
      width: 60px;
      height: 16px;
      background: #f0f0f0;
      border-radius: 8px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }
  }

  .skeleton-goods {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;

    .skeleton-image {
      width: 75px;
      height: 75px;
      background: #f0f0f0;
      border-radius: 8px;
      margin-right: 12px;
      flex-shrink: 0;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }

    .skeleton-content {
      flex: 1;
      min-height: 75px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .skeleton-title {
        width: 80%;
        height: 16px;
        background: #f0f0f0;
        border-radius: 8px;
        margin-bottom: 8px;
        animation: skeleton-loading 1.5s ease-in-out infinite;
      }

      .skeleton-subtitle {
        width: 60%;
        height: 12px;
        background: #f0f0f0;
        border-radius: 6px;
        margin-bottom: 8px;
        animation: skeleton-loading 1.5s ease-in-out infinite;
      }

      .skeleton-price {
        width: 40%;
        height: 14px;
        background: #f0f0f0;
        border-radius: 7px;
        animation: skeleton-loading 1.5s ease-in-out infinite;
      }
    }
  }

  .skeleton-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .skeleton-button {
      width: 60px;
      height: 28px;
      background: #f0f0f0;
      border-radius: 14px;
      animation: skeleton-loading 1.5s ease-in-out infinite;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }

  100% {
    opacity: 1;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;

  .empty-content {
    text-align: center;

    .empty-image {
      width: 160px;
      height: 140px;
      margin-bottom: 16px;
    }

    .empty-text {
      font-size: @font-size-14;
      color: @text-color-secondary;
      margin: 0;
    }
  }
}
</style>
